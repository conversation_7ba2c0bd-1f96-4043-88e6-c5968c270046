body, html {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    background-color: #0f1622;
    color: white;
}
.player-container {
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
}
#player {
    width: 100%;
    height: 60vh; /* 视频播放器高度 */
}
.loading-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    z-index: 100;
    flex-direction: column;
}
.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 10px;
}
@keyframes spin {
    to { transform: rotate(360deg); }
}
.error-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: none;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    z-index: 100;
    flex-direction: column;
    text-align: center;
    padding: 1rem;
}
.error-icon {
    font-size: 48px;
    margin-bottom: 10px;
}
.episode-active {
    background-color: #3b82f6 !important;
    border-color: #60a5fa !important;
}
.episode-grid {
    max-height: 30vh;
    overflow-y: auto;
}
/* 恢复播放位置提示样式 */
.position-restore-hint {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(100%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    z-index: 1000;
    transition: transform 0.3s ease;
    font-size: 14px;
}
.position-restore-hint.show {
    transform: translateX(-50%) translateY(0);
}
.hint-content {
    display: flex;
    align-items: center;
    justify-content: center;
}
.switch {
    position: relative;
    display: inline-block;
    width: 46px;
    height: 24px;
}
.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}
.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #333;
    transition: .4s;
    border-radius: 24px;
}
.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}
input:checked + .slider {
    background-color: #00ccff;
}
input:checked + .slider:before {
    transform: translateX(22px);
}
/* 添加快捷键提示样式 */
.shortcut-hint {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}
.shortcut-hint.show {
    opacity: 1;
}

/* 原生全屏时，播放器容器铺满 */
.player-container:-webkit-full-screen,
.player-container:fullscreen {
    position: fixed;
    top: 0; left: 0;
    width: 100vw; height: 100vh;
    z-index: 10000;
    background-color: #000;
}
.player-container:-webkit-full-screen #player,
.player-container:fullscreen #player {
    width: 100%; height: 100%;
}

/* 资源信息卡片区 */
#resourceInfoBarContainer {
    align-items: center;
    border-radius: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.resource-info-bar-left {
    align-items: center;
    font-size: 1.1rem;
    /* 调整字体大小 */
    font-weight: bold;
    color: #fff;
    /* 文字颜色改为白色 */
    flex: 1;
}

.resource-info-bar-videos {
    font-size: 1rem;
    /* 调整字体大小 */
    font-weight: normal;
    margin-left: 10px;
    color: #ccc;
    /* 副文本颜色调亮 */
}

.resource-switch-btn {
    align-items: center;
    background: none;
    /* 移除背景 */
    border: none;
    /* 移除边框 */
    color: #a67c2d;
    /* 恢复原图标颜色 */
    font-weight: bold;
    font-size: 1rem;
    cursor: pointer;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 0.5rem;
    transition: background 0.2s;
}

.resource-switch-btn:hover {
    background: #f5e9d7;
    /* 调整悬停背景色 */
}

.resource-switch-btn:active {
    background: #f5e9d7;
    /* 调整点击背景色 */
}

.resource-switch-icon {
    width: 20px;
    height: 20px;
    margin-right: 0;
    /* 使用gap控制间距 */
    color: #a67c2d;
    /* 恢复原图标颜色 */
    vertical-align: middle;
    transition: transform 0.3s;
}

/* 新增：移动端响应式样式 */
@media (max-width: 640px) {
    .episode-grid {
        max-height: 40vh; /* 移动端增加集数列表高度 */
    }

    /* 改进移动端按钮显示 */
    button {
        white-space: nowrap;
    }

    /* 控制栏在小屏幕上可能需要换行 */
    .player-container .flex-wrap {
        margin-bottom: 4px;
    }
}
